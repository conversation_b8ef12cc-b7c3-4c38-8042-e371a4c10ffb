/**
 * Location utilities for distance calculation and validation
 */

import { LOCATION_CONFIG } from '@/utils/config.js';

/**
 * Calculate distance between two coordinates using Haversine formula
 * @param {number} lat1 - Latitude of first point
 * @param {number} lon1 - Longitude of first point  
 * @param {number} lat2 - Latitude of second point
 * @param {number} lon2 - Longitude of second point
 * @returns {number} Distance in meters
 */
export const calculateDistance = (lat1, lon1, lat2, lon2) => {
  const R = 6371000; // Earth's radius in meters
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLon = (lon2 - lon1) * Math.PI / 180;
  const a = 
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
    Math.sin(dLon / 2) * Math.sin(dLon / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  const distance = R * c;
  return distance;
};

/**
 * Get current location of the nurse
 * @returns {Promise<{latitude: number, longitude: number}>}
 */
export const getCurrentLocation = () => {
  return new Promise((resolve, reject) => {
    uni.getLocation({
      type: 'gcj02',
      success: (res) => {
        resolve({
          latitude: res.latitude,
          longitude: res.longitude
        });
      },
      fail: (err) => {
        console.error('获取当前位置失败:', err);
        reject(err);
      }
    });
  });
};

/**
 * Check location permission and request if needed
 * @returns {Promise<boolean>}
 */
export const checkLocationPermission = () => {
  return new Promise((resolve) => {
    uni.authorize({
      scope: 'scope.userLocation',
      success: () => {
        console.log('位置权限已授权');
        resolve(true);
      },
      fail: (err) => {
        console.log('位置权限未授权:', err);
        // Show permission request modal
        uni.showModal({
          title: '位置权限申请',
          content: '需要获取您的位置信息以确保服务安全，请在设置中开启位置权限',
          confirmText: '去设置',
          success: (res) => {
            if (res.confirm) {
              uni.openSetting({
                success: (settingRes) => {
                  if (settingRes.authSetting['scope.userLocation']) {
                    resolve(true);
                  } else {
                    resolve(false);
                  }
                }
              });
            } else {
              resolve(false);
            }
          }
        });
      }
    });
  });
};

/**
 * Validate if nurse is within acceptable distance from patient
 * @param {number} nurseLatitude - Nurse's current latitude
 * @param {number} nurseLongitude - Nurse's current longitude
 * @param {number} patientLatitude - Patient's latitude
 * @param {number} patientLongitude - Patient's longitude
 * @returns {Object} Validation result with distance and isValid flag
 */
export const validateServiceDistance = (nurseLatitude, nurseLongitude, patientLatitude, patientLongitude) => {
  const distance = calculateDistance(nurseLatitude, nurseLongitude, patientLatitude, patientLongitude);
  const isValid = distance <= LOCATION_CONFIG.MAX_SERVICE_DISTANCE;
  
  return {
    distance: Math.round(distance), // Round to nearest meter
    isValid,
    maxDistance: LOCATION_CONFIG.MAX_SERVICE_DISTANCE
  };
};

/**
 * Show distance warning modal to nurse
 * @param {number} distance - Actual distance in meters
 * @param {number} maxDistance - Maximum allowed distance in meters
 * @returns {Promise<boolean>} Whether nurse confirmed to proceed
 */
export const showDistanceWarning = (distance, maxDistance) => {
  return new Promise((resolve) => {
    const distanceKm = (distance / 1000).toFixed(2);
    const maxDistanceKm = (maxDistance / 1000).toFixed(2);
    
    uni.showModal({
      title: '距离警告',
      content: `您当前距离患者 ${distanceKm}公里，超出了建议服务距离（${maxDistanceKm}公里）。\n\n为确保服务质量和安全，建议您先到达患者附近再开始服务。\n\n是否仍要继续？`,
      confirmText: '继续服务',
      cancelText: '取消',
      confirmColor: '#ff5050',
      success: (res) => {
        resolve(res.confirm);
      },
      fail: () => {
        resolve(false);
      }
    });
  });
};

/**
 * Complete location check process for starting service
 * @param {Object} patient - Patient object with latitude and longitude
 * @returns {Promise<boolean>} Whether location check passed and nurse can proceed
 */
export const performLocationCheck = async (patient) => {
  try {
    // Check if patient has location data
    if (!patient.latitude || !patient.longitude) {
      uni.showToast({
        title: '患者位置信息缺失，无法进行距离检查',
        icon: 'none',
        duration: 3000
      });
      return true; // Allow service to continue if patient location is missing
    }

    // Check location permission
    const hasPermission = await checkLocationPermission();
    if (!hasPermission) {
      uni.showToast({
        title: '需要位置权限才能开始服务',
        icon: 'none',
        duration: 3000
      });
      return false;
    }

    // Get nurse's current location
    const nurseLocation = await getCurrentLocation();
    
    // Validate distance
    const validation = validateServiceDistance(
      nurseLocation.latitude,
      nurseLocation.longitude,
      patient.latitude,
      patient.longitude
    );

    console.log(`距离检查结果: ${validation.distance}米, 是否有效: ${validation.isValid}`);

    // If distance is acceptable, allow service
    if (validation.isValid) {
      return true;
    }

    // If distance is too large, show warning and get confirmation
    const shouldProceed = await showDistanceWarning(validation.distance, validation.maxDistance);
    return shouldProceed;

  } catch (error) {
    console.error('位置检查失败:', error);
    
    // Show error and ask if nurse wants to proceed without location check
    return new Promise((resolve) => {
      uni.showModal({
        title: '位置检查失败',
        content: '无法获取您的位置信息，可能是网络问题或权限限制。\n\n是否仍要开始服务？',
        confirmText: '继续',
        cancelText: '取消',
        success: (res) => {
          resolve(res.confirm);
        },
        fail: () => {
          resolve(false);
        }
      });
    });
  }
};
