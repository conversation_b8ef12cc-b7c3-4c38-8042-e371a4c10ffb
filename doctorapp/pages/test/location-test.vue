<template>
  <view class="test-container">
    <view class="header">
      <text class="title">位置距离测试</text>
    </view>

    <view class="test-section">
      <text class="section-title">当前位置</text>
      <view class="location-info">
        <text>纬度: {{ currentLocation.latitude || '未获取' }}</text>
        <text>经度: {{ currentLocation.longitude || '未获取' }}</text>
      </view>
      <button class="test-btn" @click="getCurrentLocationTest">获取当前位置</button>
    </view>

    <view class="test-section">
      <text class="section-title">模拟患者位置</text>
      <view class="input-group">
        <text>纬度:</text>
        <input v-model="patientLocation.latitude" type="number" placeholder="34.259169" />
      </view>
      <view class="input-group">
        <text>经度:</text>
        <input v-model="patientLocation.longitude" type="number" placeholder="108.917916" />
      </view>
    </view>

    <view class="test-section">
      <text class="section-title">距离计算结果</text>
      <view class="result-info">
        <text>距离: {{ distanceResult.distance || '未计算' }}米</text>
        <text>是否在范围内: {{ distanceResult.isValid ? '是' : '否' }}</text>
        <text>最大允许距离: {{ distanceResult.maxDistance || 500 }}米</text>
      </view>
      <button class="test-btn" @click="calculateDistanceTest">计算距离</button>
    </view>

    <view class="test-section">
      <text class="section-title">完整位置检查测试</text>
      <button class="test-btn primary" @click="performLocationCheckTest">执行位置检查</button>
    </view>

    <view class="test-section">
      <text class="section-title">预设测试场景</text>
      <button class="test-btn" @click="testNearbyLocation">测试附近位置 (100米)</button>
      <button class="test-btn" @click="testFarLocation">测试远距离位置 (1000米)</button>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { 
  getCurrentLocation, 
  calculateDistance, 
  validateServiceDistance, 
  performLocationCheck 
} from '@/utils/location.js';
import { LOCATION_CONFIG } from '@/utils/config.js';

const currentLocation = reactive({
  latitude: null,
  longitude: null
});

const patientLocation = reactive({
  latitude: '34.259169',
  longitude: '108.917916'
});

const distanceResult = reactive({
  distance: null,
  isValid: null,
  maxDistance: LOCATION_CONFIG.MAX_SERVICE_DISTANCE
});

const getCurrentLocationTest = async () => {
  try {
    const location = await getCurrentLocation();
    currentLocation.latitude = location.latitude;
    currentLocation.longitude = location.longitude;
    uni.showToast({ title: '位置获取成功', icon: 'success' });
  } catch (error) {
    console.error('获取位置失败:', error);
    uni.showToast({ title: '位置获取失败', icon: 'none' });
  }
};

const calculateDistanceTest = () => {
  if (!currentLocation.latitude || !patientLocation.latitude) {
    uni.showToast({ title: '请先获取当前位置和设置患者位置', icon: 'none' });
    return;
  }

  const validation = validateServiceDistance(
    parseFloat(currentLocation.latitude),
    parseFloat(currentLocation.longitude),
    parseFloat(patientLocation.latitude),
    parseFloat(patientLocation.longitude)
  );

  distanceResult.distance = validation.distance;
  distanceResult.isValid = validation.isValid;
  distanceResult.maxDistance = validation.maxDistance;
};

const performLocationCheckTest = async () => {
  const mockPatient = {
    latitude: parseFloat(patientLocation.latitude),
    longitude: parseFloat(patientLocation.longitude)
  };

  try {
    const result = await performLocationCheck(mockPatient);
    uni.showModal({
      title: '位置检查结果',
      content: result ? '检查通过，可以开始服务' : '检查未通过或用户取消',
      showCancel: false
    });
  } catch (error) {
    console.error('位置检查失败:', error);
    uni.showToast({ title: '位置检查失败', icon: 'none' });
  }
};

const testNearbyLocation = () => {
  // Set a location 100 meters away (approximately)
  patientLocation.latitude = '34.260169'; // ~100m north
  patientLocation.longitude = '108.917916';
  calculateDistanceTest();
};

const testFarLocation = () => {
  // Set a location 1000 meters away (approximately)
  patientLocation.latitude = '34.269169'; // ~1000m north
  patientLocation.longitude = '108.917916';
  calculateDistanceTest();
};
</script>

<style scoped>
.test-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 30px;
}

.title {
  font-size: 20px;
  font-weight: bold;
  color: #333;
}

.test-section {
  background-color: white;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10px;
}

.location-info, .result-info {
  margin-bottom: 10px;
}

.location-info text, .result-info text {
  display: block;
  margin-bottom: 5px;
  color: #666;
}

.input-group {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.input-group text {
  width: 60px;
  color: #333;
}

.input-group input {
  flex: 1;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin-left: 10px;
}

.test-btn {
  background-color: #667eea;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 10px 15px;
  margin: 5px;
  font-size: 14px;
}

.test-btn.primary {
  background-color: #52c41a;
}

.test-btn:active {
  opacity: 0.8;
}
</style>
