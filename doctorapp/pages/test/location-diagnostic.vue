<template>
  <view class="diagnostic-container">
    <view class="header">
      <text class="title">位置API诊断工具</text>
    </view>

    <view class="test-section">
      <text class="section-title">配置检查</text>
      <view class="result-item">
        <text class="label">uni对象:</text>
        <text class="value">{{ uniAvailable ? '✅ 可用' : '❌ 不可用' }}</text>
      </view>
      <view class="result-item">
        <text class="label">wx对象:</text>
        <text class="value">{{ wxAvailable ? '✅ 可用' : '❌ 不可用' }}</text>
      </view>
      <view class="result-item">
        <text class="label">getLocation方法:</text>
        <text class="value">{{ getLocationAvailable ? '✅ 可用' : '❌ 不可用' }}</text>
      </view>
    </view>

    <view class="test-section">
      <text class="section-title">权限检查</text>
      <view class="result-item">
        <text class="label">权限状态:</text>
        <text class="value">{{ permissionStatus }}</text>
      </view>
      <button class="test-btn" @click="checkPermission">检查权限</button>
    </view>

    <view class="test-section">
      <text class="section-title">位置获取测试</text>
      <view class="result-item">
        <text class="label">测试状态:</text>
        <text class="value">{{ locationTestStatus }}</text>
      </view>
      <view v-if="locationResult" class="result-item">
        <text class="label">位置结果:</text>
        <text class="value">{{ locationResult }}</text>
      </view>
      <view v-if="locationError" class="error-item">
        <text class="label">错误信息:</text>
        <text class="error-text">{{ locationError }}</text>
      </view>
      <button class="test-btn" @click="testLocation" :disabled="testing">
        {{ testing ? '测试中...' : '测试位置获取' }}
      </button>
    </view>

    <view class="test-section">
      <text class="section-title">原生API测试</text>
      <button class="test-btn" @click="testNativeWxLocation">测试 wx.getLocation</button>
      <button class="test-btn" @click="testUniLocation">测试 uni.getLocation</button>
    </view>

    <view class="test-section">
      <text class="section-title">系统信息</text>
      <view class="result-item">
        <text class="label">平台:</text>
        <text class="value">{{ systemInfo.platform || '未知' }}</text>
      </view>
      <view class="result-item">
        <text class="label">微信版本:</text>
        <text class="value">{{ systemInfo.version || '未知' }}</text>
      </view>
      <view class="result-item">
        <text class="label">基础库版本:</text>
        <text class="value">{{ systemInfo.SDKVersion || '未知' }}</text>
      </view>
    </view>

    <view class="test-section">
      <text class="section-title">解决方案</text>
      <view class="solution-item">
        <text class="solution-title">1. 重启开发工具</text>
        <text class="solution-desc">关闭微信开发者工具，重新打开项目</text>
      </view>
      <view class="solution-item">
        <text class="solution-title">2. 清除缓存</text>
        <text class="solution-desc">工具 → 清除缓存 → 清除全部缓存</text>
      </view>
      <view class="solution-item">
        <text class="solution-title">3. 重新编译</text>
        <text class="solution-desc">删除 unpackage 目录，重新编译项目</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue';

const uniAvailable = ref(false);
const wxAvailable = ref(false);
const getLocationAvailable = ref(false);
const permissionStatus = ref('未检查');
const locationTestStatus = ref('未测试');
const locationResult = ref('');
const locationError = ref('');
const testing = ref(false);
const systemInfo = ref({});

onMounted(() => {
  checkEnvironment();
  getSystemInfo();
});

const checkEnvironment = () => {
  uniAvailable.value = typeof uni !== 'undefined';
  wxAvailable.value = typeof wx !== 'undefined';
  getLocationAvailable.value = uniAvailable.value && typeof uni.getLocation === 'function';
};

const getSystemInfo = () => {
  if (typeof uni !== 'undefined' && uni.getSystemInfo) {
    uni.getSystemInfo({
      success: (res) => {
        systemInfo.value = res;
      }
    });
  }
};

const checkPermission = () => {
  if (!uniAvailable.value) {
    permissionStatus.value = '❌ uni对象不可用';
    return;
  }

  uni.getSetting({
    success: (res) => {
      const locationAuth = res.authSetting['scope.userLocation'];
      if (locationAuth === true) {
        permissionStatus.value = '✅ 已授权';
      } else if (locationAuth === false) {
        permissionStatus.value = '❌ 已拒绝';
      } else {
        permissionStatus.value = '⚠️ 未询问';
      }
    },
    fail: (err) => {
      permissionStatus.value = `❌ 检查失败: ${err.errMsg}`;
    }
  });
};

const testLocation = async () => {
  if (!getLocationAvailable.value) {
    locationTestStatus.value = '❌ getLocation方法不可用';
    return;
  }

  testing.value = true;
  locationTestStatus.value = '🔄 测试中...';
  locationError.value = '';
  locationResult.value = '';

  try {
    const result = await new Promise((resolve, reject) => {
      uni.getLocation({
        type: 'gcj02',
        success: resolve,
        fail: reject
      });
    });

    locationTestStatus.value = '✅ 测试成功';
    locationResult.value = `纬度: ${result.latitude}, 经度: ${result.longitude}`;
  } catch (error) {
    locationTestStatus.value = '❌ 测试失败';
    locationError.value = error.errMsg || error.message || JSON.stringify(error);
  } finally {
    testing.value = false;
  }
};

const testNativeWxLocation = () => {
  if (typeof wx === 'undefined' || !wx.getLocation) {
    uni.showToast({ title: 'wx.getLocation 不可用', icon: 'none' });
    return;
  }

  wx.getLocation({
    type: 'gcj02',
    success: (res) => {
      uni.showModal({
        title: 'wx.getLocation 成功',
        content: `纬度: ${res.latitude}\n经度: ${res.longitude}`,
        showCancel: false
      });
    },
    fail: (err) => {
      uni.showModal({
        title: 'wx.getLocation 失败',
        content: err.errMsg || JSON.stringify(err),
        showCancel: false
      });
    }
  });
};

const testUniLocation = () => {
  if (!getLocationAvailable.value) {
    uni.showToast({ title: 'uni.getLocation 不可用', icon: 'none' });
    return;
  }

  uni.getLocation({
    type: 'gcj02',
    success: (res) => {
      uni.showModal({
        title: 'uni.getLocation 成功',
        content: `纬度: ${res.latitude}\n经度: ${res.longitude}`,
        showCancel: false
      });
    },
    fail: (err) => {
      uni.showModal({
        title: 'uni.getLocation 失败',
        content: err.errMsg || JSON.stringify(err),
        showCancel: false
      });
    }
  });
};
</script>

<style scoped>
.diagnostic-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 30px;
}

.title {
  font-size: 20px;
  font-weight: bold;
  color: #333;
}

.test-section {
  background-color: white;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10px;
}

.result-item, .error-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  padding: 5px 0;
}

.label {
  color: #666;
  font-size: 14px;
}

.value {
  color: #333;
  font-size: 14px;
  font-weight: 500;
}

.error-text {
  color: #ff4d4f;
  font-size: 12px;
  flex: 1;
  margin-left: 10px;
  word-break: break-all;
}

.test-btn {
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 10px 15px;
  margin: 5px;
  font-size: 14px;
}

.test-btn:disabled {
  background-color: #d9d9d9;
  color: #999;
}

.solution-item {
  margin-bottom: 15px;
  padding: 10px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.solution-title {
  font-weight: bold;
  color: #1890ff;
  display: block;
  margin-bottom: 5px;
}

.solution-desc {
  color: #666;
  font-size: 13px;
}
</style>
