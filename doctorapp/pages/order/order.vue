<template>
  <view class="order-container">
    <!-- Status Filter Tabs -->
    <view class="status-tabs">
      <text
        v-for="tab in statusTabs"
        :key="tab.status"
        class="tab-item"
        :class="{ 'active': currentStatus === tab.status }"
        @click="switchStatus(tab.status)">
        {{ tab.label }}
      </text>
    </view>

    <!-- Date Filter -->
    <view class="date-filter">
      <picker mode="date" :value="dateFrom" @change="onDateFromChange">
        <view class="date-picker">
          <text>开始日期: {{ dateFrom || '选择日期' }}</text>
        </view>
      </picker>
      <picker mode="date" :value="dateTo" @change="onDateToChange">
        <view class="date-picker">
          <text>结束日期: {{ dateTo || '选择日期' }}</text>
        </view>
      </picker>
      <button class="clear-filter-btn" @click="clearDateFilter">清除</button>
    </view>

    <view v-if="loading" class="loading-state">
      <text class="loading-text">加载中...</text>
    </view>
    <view v-else-if="orders.length === 0" class="empty-state">
      <image src="/static/icon/order.png" class="empty-image"></image>
      <text class="empty-text">
        {{ currentStatus ? `暂无${getStatusLabel(currentStatus)}订单` : '暂无分配给您的订单' }}
      </text>
    </view>
    <view v-else class="order-list">
      <view class="order-card" v-for="order in orders" :key="order.id">
        <view class="card-header">
          <text class="order-num">订单号: {{ order.order_num }}</text>
          <text class="order-status" :class="`status-${getStatusClass(order.status)}`">
            {{ getStatusLabel(order.status) }}
          </text>
        </view>

        <view class="card-body" @click="goToOrderDetail(order.id)">
          <view class="service-info">
            <text class="service-name">{{ order.service.name }}</text>
            <text class="patient-name">患者: {{ order.patient.name }}</text>
            <text class="service-date" v-if="order.service_date">
              服务日期: {{ formatDate(order.service_date) }}
            </text>
            <text class="service-time" v-if="order.service_time">
              服务时间: {{ order.service_time }}
            </text>
          </view>
          <view class="nurse-role">
            <text v-if="isMainNurse(order)" class="role-badge main">主护</text>
            <text v-else class="role-badge secondary">副护</text>
          </view>
        </view>

        <view class="card-footer">
          <text class="order-amount">金额: ￥{{ parseFloat(order.amount).toFixed(2) }}</text>
          <view class="action-buttons">
            <button
              v-if="order.status === 'PAID'"
              class="start-service-btn"
              @click="startService(order)"
            >
              开始服务
            </button>
            <button
              v-if="order.status === 'PROCESSING'"
              class="complete-service-btn"
              @click="completeService(order)"
            >
              完成服务
            </button>
            <button
              class="contact-patient-btn"
              @click="contactPatient(order)"
            >
              联系患者
            </button>
          </view>
        </view>

        <view v-if="order.notes" class="order-notes">
          <text class="notes-label">备注:</text>
          <text class="notes-content">{{ order.notes }}</text>
        </view>
      </view>
    </view>

    <!-- Pull to refresh and load more -->
    <view v-if="hasMore && !loading" class="load-more" @click="loadMore">
      <text>点击加载更多</text>
    </view>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue';
import { onShow, onPullDownRefresh, onReachBottom } from '@dcloudio/uni-app';
import { API_BASE_URL } from '@/utils/config.js';
import { performLocationCheck } from '@/utils/location.js';

// Order status constants
const ORDER_STATUS = {
  UNPAID: 'UNPAID',
  PAID: 'PAID',
  PROCESSING: 'PROCESSING',
  COMPLETED: 'COMPLETED',
  CANCELED: 'CANCELED',
  REFUNDING: 'REFUNDING',
  REFUNDED: 'REFUNDED',
  FAILED: 'FAILED',
  EXPIRED: 'EXPIRED'
};

const orders = ref([]);
const loading = ref(true);
const currentStatus = ref(null);
const dateFrom = ref('');
const dateTo = ref('');
const hasMore = ref(true);
const currentPage = ref(0);
const pageSize = 20;
const currentUser = ref(null);

const isStarting = ref(false);
const isCompleting = ref(false);

const statusTabs = [
  { label: '全部', status: null },
  { label: '待服务', status: ORDER_STATUS.PAID },
  { label: '服务中', status: ORDER_STATUS.PROCESSING },
  { label: '已完成', status: ORDER_STATUS.COMPLETED },
];

// Get current user info
const getCurrentUser = () => {
  try {
    const userInfo = uni.getStorageSync('userInfo');
    if (userInfo) {
      currentUser.value = userInfo;
    }
  } catch (error) {
    console.error('Failed to get user info:', error);
  }
};

onShow(() => {
  getCurrentUser();
  refreshOrders();
});

onPullDownRefresh(() => {
  refreshOrders();
});

onReachBottom(() => {
  if (hasMore.value && !loading.value) {
    loadMore();
  }
});

const refreshOrders = () => {
  currentPage.value = 0;
  hasMore.value = true;
  orders.value = [];
  fetchOrders();
};

const fetchOrders = () => {
  const token = uni.getStorageSync('token');
  if (!token) {
    loading.value = false;
    uni.navigateTo({ url: '/pages/login/login' });
    return;
  }

  loading.value = true;

  let url = `${API_BASE_URL}/orders/nurse/assigned?skip=${currentPage.value * pageSize}&limit=${pageSize}`;
  if (currentStatus.value) {
    url += `&status=${encodeURIComponent(currentStatus.value)}`;
  }
  if (dateFrom.value) {
    url += `&date_from=${dateFrom.value}`;
  }
  if (dateTo.value) {
    url += `&date_to=${dateTo.value}`;
  }

  uni.request({
    url: url,
    method: 'GET',
    header: { 'Authorization': `Bearer ${token}` },
    success: (res) => {
      if (res.statusCode === 200) {
        const ordersData = res.data.orders || [];

        if (currentPage.value === 0) {
          orders.value = ordersData;
        } else {
          orders.value = [...orders.value, ...ordersData];
        }

        hasMore.value = ordersData.length === pageSize;
        currentPage.value++;
      } else {
        uni.showToast({ title: '加载订单失败', icon: 'none' });
      }
    },
    fail: () => {
      uni.showToast({ title: '网络错误', icon: 'none' });
    },
    complete: () => {
      loading.value = false;
      uni.stopPullDownRefresh();
    }
  });
};

const loadMore = () => {
  if (!hasMore.value || loading.value) return;
  fetchOrders();
};

const switchStatus = (status) => {
  if (currentStatus.value === status) return;

  currentStatus.value = status;
  refreshOrders();
};

const onDateFromChange = (e) => {
  dateFrom.value = e.detail.value;
  refreshOrders();
};

const onDateToChange = (e) => {
  dateTo.value = e.detail.value;
  refreshOrders();
};

const clearDateFilter = () => {
  dateFrom.value = '';
  dateTo.value = '';
  refreshOrders();
};

const isMainNurse = (order) => {
  return currentUser.value && order.primary_nurse && order.primary_nurse.user_id === currentUser.value.user_id;
};

const getStatusClass = (status) => {
  const statusMap = {
    'UNPAID': 'unpaid',
    'PAID': 'paid',
    'PROCESSING': 'processing',
    'COMPLETED': 'completed',
    'CANCELED': 'canceled',
    'REFUNDING': 'refunding',
    'REFUNDED': 'refunded',
    'FAILED': 'failed',
    'EXPIRED': 'expired'
  };
  return statusMap[status] || 'unknown';
};

const getStatusLabel = (status) => {
  const statusMap = {
    'UNPAID': '待支付',
    'PAID': '待服务',
    'PROCESSING': '服务中',
    'COMPLETED': '已完成',
    'CANCELED': '已取消',
    'REFUNDING': '退款中',
    'REFUNDED': '已退款',
    'FAILED': '支付失败',
    'EXPIRED': '已过期'
  };
  return statusMap[status] || '未知状态';
};

const formatDate = (dateString) => {
  if (!dateString) return '';
  return new Date(dateString).toLocaleDateString('zh-CN');
};

const goToOrderDetail = (orderId) => {
  uni.navigateTo({
    url: `/pages/order/detail?id=${orderId}`
  });
};

const startService = async (order) => {
  try {
    // Perform location check first
    const canProceed = await performLocationCheck(order.patient);
    if (!canProceed) {
      return; // User cancelled or location check failed
    }

    // Show confirmation modal after location check passes
    uni.showModal({
      title: '开始服务',
      content: '确认开始为患者提供服务？',
      success: (res) => {
        if (res.confirm) {
          isStarting.value = true;
          const token = uni.getStorageSync('token');

          uni.request({
            url: `${API_BASE_URL}/orders/${order.id}/start`,
            method: 'PATCH',
            header: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            },
            success: (res) => {
              if (res.statusCode === 200) {
                uni.showToast({ title: '服务已开始', icon: 'success' });
                refreshOrders(); // Refresh order data
              } else {
                uni.showToast({ title: '操作失败', icon: 'none' });
              }
            },
            fail: () => {
              uni.showToast({ title: '网络错误', icon: 'none' });
            },
            complete: () => {
              isStarting.value = false;
            }
          });
        }
      }
    });
  } catch (error) {
    console.error('开始服务失败:', error);
    uni.showToast({ title: '开始服务失败', icon: 'none' });
  }
};

const completeService = (order) => {
  // TODO: Implement complete service functionality
  uni.showModal({
    title: '完成服务',
    content: '确认已完成服务？',
    success: (res) => {
      if (res.confirm) {
        isCompleting.value = true;
        const token = uni.getStorageSync('token');
        
        uni.request({
          url: `${API_BASE_URL}/orders/${order.id}/complete`,
          method: 'PATCH',
          header: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          success: (res) => {
            if (res.statusCode === 200) {
              uni.showToast({ title: '服务已完成', icon: 'success' });
              refreshOrders(); // Refresh order data
            } else {
              uni.showToast({ title: '操作失败', icon: 'none' });
            }
          },
          fail: () => {
            uni.showToast({ title: '网络错误', icon: 'none' });
          },
          complete: () => {
            isCompleting.value = false;
          }
        });
      }
    }
  });
};

const contactPatient = (order) => {
  // TODO: Implement contact patient functionality
  uni.showActionSheet({
    itemList: ['拨打电话', '发送短信'],
    success: (res) => {
      if (res.tapIndex === 0) {
        // Call patient
        uni.showToast({ title: '拨打电话功能开发中', icon: 'none' });
      } else if (res.tapIndex === 1) {
        // Send SMS
        uni.showToast({ title: '发送短信功能开发中', icon: 'none' });
      }
    }
  });
};
</script>

<style scoped>
.order-container {
  background-color: #f4f4f4;
  min-height: 100vh;
}

.status-tabs {
  display: flex;
  background-color: #fff;
  padding: 10px 0;
  border-bottom: 1px solid #f0f0f0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 8px 0;
  font-size: 14px;
  color: #666;
  border-bottom: 2px solid transparent;
}

.tab-item.active {
  color: #667eea;
  border-bottom-color: #667eea;
  font-weight: bold;
}

.date-filter {
  display: flex;
  align-items: center;
  background-color: #fff;
  padding: 10px 15px;
  border-bottom: 1px solid #f0f0f0;
  gap: 10px;
}

.date-picker {
  flex: 1;
  padding: 8px 12px;
  background-color: #f8f8f8;
  border-radius: 4px;
  font-size: 14px;
  color: #333;
}

.clear-filter-btn {
  background-color: #667eea;
  color: white;
  font-size: 12px;
  padding: 8px 12px;
  border-radius: 4px;
  border: none;
}

.loading-state, .empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
  color: #999;
}

.loading-text {
  font-size: 16px;
}

.empty-image {
  width: 100px;
  height: 100px;
  margin-bottom: 20px;
}

.empty-text {
  font-size: 16px;
  margin-bottom: 20px;
}

.order-list {
  padding: 10px;
}

.order-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 10px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 10px;
  margin-bottom: 10px;
}

.order-num {
  font-size: 12px;
  color: #999;
}

.order-status {
  font-size: 14px;
  font-weight: bold;
}

.status-unpaid { color: #faad14; }
.status-paid { color: #52c41a; }
.status-processing { color: #1890ff; }
.status-completed { color: #52c41a; }
.status-canceled { color: #999; }
.status-refunding { color: #faad14; }
.status-refunded { color: #999; }
.status-failed { color: #ff4d4f; }
.status-expired { color: #999; }

.card-body {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 10px;
}

.service-info {
  flex: 1;
}

.service-name {
  font-weight: bold;
  font-size: 16px;
  display: block;
  margin-bottom: 5px;
  color: #333;
}

.patient-name, .service-date, .service-time {
  font-size: 14px;
  color: #666;
  display: block;
  margin-bottom: 3px;
}

.nurse-role {
  margin-left: 10px;
}

.role-badge {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 12px;
  color: white;
}

.role-badge.main {
  background-color: #667eea;
}

.role-badge.secondary {
  background-color: #52c41a;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.order-amount {
  font-size: 15px;
  font-weight: bold;
  color: #ff5050;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.start-service-btn {
  background-color: #52c41a;
  color: white;
  font-size: 12px;
  padding: 4px 12px;
  border-radius: 4px;
  border: none;
}

.complete-service-btn {
  background-color: #1890ff;
  color: white;
  font-size: 12px;
  padding: 4px 12px;
  border-radius: 4px;
  border: none;
}

.contact-patient-btn {
  background-color: #667eea;
  color: white;
  font-size: 12px;
  padding: 4px 12px;
  border-radius: 4px;
  border: none;
}

.order-notes {
  border-top: 1px solid #f0f0f0;
  padding-top: 10px;
  margin-top: 10px;
}

.notes-label {
  font-size: 12px;
  color: #999;
  margin-right: 5px;
}

.notes-content {
  font-size: 14px;
  color: #666;
}

.load-more {
  text-align: center;
  padding: 20px;
  color: #666;
  font-size: 14px;
}
</style>
