/**
 * <PERSON><PERSON><PERSON> to fix WeChat mini program location configuration
 * Run this script to ensure proper location API configuration
 */

const fs = require('fs');
const path = require('path');

// Paths
const appJsonPath = path.join(__dirname, '../unpackage/dist/dev/mp-weixin/app.json');
const manifestJsonPath = path.join(__dirname, '../manifest.json');

console.log('🔧 Fixing WeChat mini program location configuration...');

// Function to update app.json
function updateAppJson() {
  try {
    if (!fs.existsSync(appJsonPath)) {
      console.log('❌ app.json not found. Please compile the project first.');
      return false;
    }

    const appJson = JSON.parse(fs.readFileSync(appJsonPath, 'utf8'));
    
    // Add requiredPrivateInfos if not present
    if (!appJson.requiredPrivateInfos) {
      appJson.requiredPrivateInfos = [];
    }
    
    // Add getLocation if not present
    if (!appJson.requiredPrivateInfos.includes('getLocation')) {
      appJson.requiredPrivateInfos.push('getLocation');
    }
    
    // Ensure permission is set
    if (!appJson.permission) {
      appJson.permission = {};
    }
    
    if (!appJson.permission['scope.userLocation']) {
      appJson.permission['scope.userLocation'] = {
        desc: '获取医护人员位置信息, 确保安全'
      };
    }
    
    // Write back to file
    fs.writeFileSync(appJsonPath, JSON.stringify(appJson, null, 2));
    console.log('✅ app.json updated successfully');
    return true;
    
  } catch (error) {
    console.error('❌ Error updating app.json:', error.message);
    return false;
  }
}

// Function to verify manifest.json
function verifyManifestJson() {
  try {
    if (!fs.existsSync(manifestJsonPath)) {
      console.log('❌ manifest.json not found');
      return false;
    }

    const manifestContent = fs.readFileSync(manifestJsonPath, 'utf8');
    const manifest = JSON.parse(manifestContent);
    
    // Check mp-weixin configuration
    if (!manifest['mp-weixin']) {
      console.log('❌ mp-weixin configuration missing in manifest.json');
      return false;
    }
    
    const mpWeixin = manifest['mp-weixin'];
    
    // Check requiredPrivateInfos
    if (!mpWeixin.requiredPrivateInfos || !mpWeixin.requiredPrivateInfos.includes('getLocation')) {
      console.log('⚠️  requiredPrivateInfos missing or incomplete in manifest.json');
      
      // Fix it
      if (!mpWeixin.requiredPrivateInfos) {
        mpWeixin.requiredPrivateInfos = [];
      }
      if (!mpWeixin.requiredPrivateInfos.includes('getLocation')) {
        mpWeixin.requiredPrivateInfos.push('getLocation');
      }
      
      // Write back
      fs.writeFileSync(manifestJsonPath, JSON.stringify(manifest, null, 4));
      console.log('✅ manifest.json updated');
    }
    
    // Check permission
    if (!mpWeixin.permission || !mpWeixin.permission['scope.userLocation']) {
      console.log('⚠️  Location permission missing in manifest.json');
      return false;
    }
    
    console.log('✅ manifest.json configuration verified');
    return true;
    
  } catch (error) {
    console.error('❌ Error verifying manifest.json:', error.message);
    return false;
  }
}

// Main execution
function main() {
  console.log('Starting configuration fix...\n');
  
  const manifestOk = verifyManifestJson();
  const appJsonOk = updateAppJson();
  
  console.log('\n📋 Summary:');
  console.log(`- manifest.json: ${manifestOk ? '✅ OK' : '❌ Issues found'}`);
  console.log(`- app.json: ${appJsonOk ? '✅ Updated' : '❌ Failed to update'}`);
  
  if (manifestOk && appJsonOk) {
    console.log('\n🎉 Configuration fix completed successfully!');
    console.log('\n📝 Next steps:');
    console.log('1. Restart WeChat Developer Tools');
    console.log('2. Clean compile the project (删除 → 编译)');
    console.log('3. Test the location functionality');
  } else {
    console.log('\n❌ Some issues remain. Please check the errors above.');
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = {
  updateAppJson,
  verifyManifestJson,
  main
};
