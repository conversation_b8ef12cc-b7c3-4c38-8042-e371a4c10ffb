# 护士服务距离检查功能

## 功能概述

当护士尝试开始服务时，系统会自动获取护士的当前位置，并计算与患者位置的距离。如果距离超过预设的安全范围，系统会弹出警告提示，确保服务质量和安全。

## 功能特性

### 1. 自动位置检查
- 护士点击"开始服务"时自动触发
- 获取护士当前GPS位置
- 计算与患者位置的直线距离
- 使用Haversine公式确保计算精度

### 2. 智能警告系统
- 距离在安全范围内：直接允许开始服务
- 距离超出安全范围：显示警告弹窗
- 护士可以选择继续服务或取消操作

### 3. 权限管理
- 自动检查位置权限
- 未授权时引导用户开启权限
- 权限获取失败时提供备选方案

### 4. 容错处理
- 患者位置信息缺失时允许继续服务
- 网络错误时提供手动确认选项
- GPS获取失败时显示友好提示

## 配置参数

### 距离阈值设置
```javascript
// doctorapp/utils/config.js
export const LOCATION_CONFIG = {
  MAX_SERVICE_DISTANCE: 500, // 最大允许距离（米）
  SERVICE_CENTER: {
    latitude: 34.259169,    // 服务中心纬度
    longitude: 108.917916   // 服务中心经度
  }
};
```

### 可调整参数
- `MAX_SERVICE_DISTANCE`: 最大允许服务距离（默认500米）
- `SERVICE_CENTER`: 服务区域中心坐标（用于备用计算）

## 技术实现

### 核心文件
1. `doctorapp/utils/location.js` - 位置服务工具类
2. `doctorapp/utils/config.js` - 配置文件
3. `doctorapp/pages/order/order.vue` - 订单列表页面
4. `doctorapp/pages/order/detail.vue` - 订单详情页面

### 主要函数
- `getCurrentLocation()` - 获取当前位置
- `calculateDistance()` - 计算两点距离
- `validateServiceDistance()` - 验证服务距离
- `performLocationCheck()` - 执行完整位置检查
- `showDistanceWarning()` - 显示距离警告

### 距离计算公式
使用Haversine公式计算地球表面两点间的最短距离：
```javascript
const R = 6371000; // 地球半径（米）
const dLat = (lat2 - lat1) * Math.PI / 180;
const dLon = (lon2 - lon1) * Math.PI / 180;
const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
          Math.cos(lat1 * Math.PI/180) * Math.cos(lat2 * Math.PI/180) *
          Math.sin(dLon/2) * Math.sin(dLon/2);
const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
const distance = R * c;
```

## 用户体验流程

### 正常流程
1. 护士点击"开始服务"
2. 系统检查位置权限
3. 获取护士当前位置
4. 计算与患者距离
5. 距离合适 → 显示确认弹窗 → 开始服务

### 距离超限流程
1. 护士点击"开始服务"
2. 系统检查位置权限
3. 获取护士当前位置
4. 计算与患者距离
5. 距离超限 → 显示警告弹窗
6. 护士选择：
   - "继续服务" → 显示确认弹窗 → 开始服务
   - "取消" → 返回订单页面

### 异常处理流程
- 位置权限未授权 → 引导开启权限
- GPS获取失败 → 询问是否继续
- 患者位置缺失 → 允许继续服务
- 网络错误 → 提供手动确认

## 测试功能

### 测试页面
访问 `doctorapp/pages/test/location-test.vue` 进行功能测试：
- 获取当前位置
- 设置模拟患者位置
- 计算距离结果
- 执行完整位置检查
- 预设测试场景

### 测试场景
1. **近距离测试**：设置100米内的患者位置
2. **远距离测试**：设置1000米外的患者位置
3. **权限测试**：测试位置权限获取
4. **异常测试**：模拟网络错误等异常情况

## 部署注意事项

### 权限配置
确保 `manifest.json` 中包含位置权限配置：
```json
"permission": {
  "scope.userLocation": {
    "desc": "获取医护人员位置信息, 确保安全"
  }
}
```

### 生产环境配置
- 根据实际服务区域调整 `MAX_SERVICE_DISTANCE`
- 更新 `SERVICE_CENTER` 坐标为实际服务中心
- 考虑不同地区的距离阈值差异

### 性能优化
- 位置获取有超时机制
- 距离计算使用高效算法
- 避免频繁的位置请求

## 未来扩展

### 可能的改进方向
1. **动态距离阈值**：根据服务类型调整距离限制
2. **路径距离计算**：使用实际路径距离替代直线距离
3. **历史位置记录**：记录护士服务轨迹
4. **区域服务限制**：限制特定区域内的服务
5. **实时位置追踪**：服务过程中的位置监控
